<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools" android:id="@+id/main" android:layout_width="match_parent" android:layout_height="match_parent" android:padding="16dp" tools:context=".MainActivity">

    <TextView android:id="@+id/titleTextView" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="NFC Tester" android:textSize="24sp" android:textStyle="bold" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" android:layout_marginTop="32dp" />

    <TextView android:id="@+id/statusTextView" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="In attesa di tag NFC..." android:textSize="16sp" android:textColor="#666666" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/titleTextView" android:layout_marginTop="16dp" />

    <!-- Sezione per la scrittura NFC -->
    <LinearLayout android:id="@+id/writeSection" android:layout_width="0dp" android:layout_height="wrap_content" android:orientation="vertical" android:layout_marginTop="24dp" android:background="#e8f5e8" android:padding="12dp" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/statusTextView">

        <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Scrittura NFC" android:textSize="18sp" android:textStyle="bold" android:textColor="#2e7d32" android:layout_marginBottom="8dp" />

        <Spinner android:id="@+id/writeTypeSpinner" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginBottom="8dp" />

        <EditText android:id="@+id/writeDataEditText" android:layout_width="match_parent" android:layout_height="wrap_content" android:hint="Inserisci il testo da scrivere..." android:minLines="2" android:maxLines="4" android:layout_marginBottom="8dp" />

        <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:orientation="horizontal">

            <Button android:id="@+id/writeButton" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_weight="1" android:text="Scrivi su Tag" android:layout_marginEnd="8dp" android:backgroundTint="#4caf50" />

            <Button android:id="@+id/clearButton" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_weight="1" android:text="Cancella" android:backgroundTint="#ff9800" />

        </LinearLayout>

    </LinearLayout>

    <ScrollView android:id="@+id/scrollView" android:layout_width="0dp" android:layout_height="0dp" android:layout_marginTop="16dp" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/writeSection">

        <TextView android:id="@+id/nfcDataTextView" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="Avvicina un tag NFC al dispositivo per vedere le informazioni..." android:textSize="14sp" android:fontFamily="monospace" android:background="#f5f5f5" android:padding="12dp" android:textIsSelectable="true" />

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>