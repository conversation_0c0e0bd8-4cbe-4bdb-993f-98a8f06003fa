package com.example.jetnfctester;

import android.app.Activity;
import android.app.PendingIntent;
import android.content.Intent;
import android.content.IntentFilter;
import android.nfc.NdefMessage;
import android.nfc.NdefRecord;
import android.nfc.NfcAdapter;
import android.nfc.Tag;
import android.nfc.tech.Ndef;
import android.os.Parcelable;
import android.widget.Toast;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;

/**
 * Gestisce tutte le operazioni NFC dell'applicazione
 */
public class NFCManager {
    
    private Activity activity;
    private NfcAdapter nfcAdapter;
    private PendingIntent pendingIntent;
    private IntentFilter[] intentFiltersArray;
    private String[][] techListsArray;
    
    // Interface per comunicare con l'activity
    public interface NFCListener {
        void onNFCStatusChanged(String status);
        void onNFCDataReceived(String data);
        void onNFCError(String error);
    }
    
    private NFCListener listener;
    
    public NFCManager(Activity activity, NFCListener listener) {
        this.activity = activity;
        this.listener = listener;
        initialize();
    }
    
    /**
     * Inizializza l'adapter NFC e configura i filtri
     */
    private void initialize() {
        nfcAdapter = NfcAdapter.getDefaultAdapter(activity);
        
        if (nfcAdapter == null) {
            listener.onNFCError("NFC non supportato su questo dispositivo");
            return;
        }
        
        if (!nfcAdapter.isEnabled()) {
            listener.onNFCError("NFC disabilitato - Abilitalo nelle impostazioni");
            return;
        }
        
        listener.onNFCStatusChanged("NFC attivo - In attesa di tag...");
        
        // Configura il PendingIntent per il foreground dispatch
        Intent intent = new Intent(activity, activity.getClass()).addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
        pendingIntent = PendingIntent.getActivity(activity, 0, intent, PendingIntent.FLAG_MUTABLE);
        
        // Configura i filtri per intercettare tutti i tipi di tag NFC
        IntentFilter ndef = new IntentFilter(NfcAdapter.ACTION_NDEF_DISCOVERED);
        IntentFilter tag = new IntentFilter(NfcAdapter.ACTION_TAG_DISCOVERED);
        IntentFilter tech = new IntentFilter(NfcAdapter.ACTION_TECH_DISCOVERED);
        
        intentFiltersArray = new IntentFilter[]{ndef, tag, tech};
        
        // Lista delle tecnologie supportate
        techListsArray = new String[][]{
            new String[]{Ndef.class.getName()}
        };
    }
    
    /**
     * Controlla lo stato NFC e aggiorna il listener
     */
    public void checkStatus() {
        if (nfcAdapter == null) {
            listener.onNFCStatusChanged("NFC non supportato su questo dispositivo");
            listener.onNFCDataReceived("Questo dispositivo non supporta la tecnologia NFC.\n\nPer utilizzare questa app è necessario un dispositivo con supporto NFC.");
            return;
        }
        
        if (!nfcAdapter.isEnabled()) {
            listener.onNFCStatusChanged("NFC disabilitato - Abilitalo nelle impostazioni");
            listener.onNFCDataReceived("NFC è disabilitato su questo dispositivo.\n\nPer utilizzare questa app:\n1. Vai nelle Impostazioni del dispositivo\n2. Cerca 'NFC' o 'Connessioni'\n3. Abilita NFC\n4. Torna a questa app");
            
            // Mostra un toast per guidare l'utente
            Toast.makeText(activity, "Abilita NFC nelle impostazioni per utilizzare l'app", Toast.LENGTH_LONG).show();
            return;
        }
        
        listener.onNFCStatusChanged("NFC attivo - In attesa di tag...");
    }
    
    /**
     * Abilita il foreground dispatch
     */
    public void enableForegroundDispatch() {
        if (nfcAdapter != null && nfcAdapter.isEnabled()) {
            nfcAdapter.enableForegroundDispatch(activity, pendingIntent, intentFiltersArray, techListsArray);
        }
    }
    
    /**
     * Disabilita il foreground dispatch
     */
    public void disableForegroundDispatch() {
        if (nfcAdapter != null) {
            nfcAdapter.disableForegroundDispatch(activity);
        }
    }
    
    /**
     * Gestisce un intent NFC ricevuto
     */
    public void handleIntent(Intent intent) {
        if (intent == null) return;
        
        String action = intent.getAction();
        
        if (NfcAdapter.ACTION_NDEF_DISCOVERED.equals(action) ||
            NfcAdapter.ACTION_TAG_DISCOVERED.equals(action) ||
            NfcAdapter.ACTION_TECH_DISCOVERED.equals(action)) {
            
            Tag tag = intent.getParcelableExtra(NfcAdapter.EXTRA_TAG);
            if (tag != null) {
                try {
                    processNfcTag(tag, intent);
                } catch (Exception e) {
                    listener.onNFCStatusChanged("Errore nella lettura del tag");
                    listener.onNFCError("=== ERRORE ===\nSi è verificato un errore durante la lettura del tag NFC:\n" + 
                                      e.getMessage() + "\n\nProva ad avvicinare nuovamente il tag.");
                }
            } else {
                listener.onNFCStatusChanged("Errore: tag non valido");
                listener.onNFCError("=== ERRORE ===\nIl tag NFC rilevato non è valido o non può essere letto.");
            }
        }
    }
    
    /**
     * Processa un tag NFC e estrae le informazioni
     */
    private void processNfcTag(Tag tag, Intent intent) {
        listener.onNFCStatusChanged("Tag NFC rilevato!");
        
        StringBuilder tagInfo = new StringBuilder();
        tagInfo.append("=== INFORMAZIONI TAG NFC ===\n\n");
        
        try {
            // Informazioni di base del tag
            byte[] tagId = tag.getId();
            if (tagId != null && tagId.length > 0) {
                tagInfo.append("ID Tag: ").append(bytesToHex(tagId)).append("\n");
            } else {
                tagInfo.append("ID Tag: Non disponibile\n");
            }
            
            String[] techList = tag.getTechList();
            if (techList != null && techList.length > 0) {
                tagInfo.append("Tecnologie supportate: ").append(Arrays.toString(techList)).append("\n\n");
            } else {
                tagInfo.append("Tecnologie supportate: Nessuna rilevata\n\n");
            }
            
            // Prova a leggere i dati NDEF
            Parcelable[] rawMessages = intent.getParcelableArrayExtra(NfcAdapter.EXTRA_NDEF_MESSAGES);
            if (rawMessages != null && rawMessages.length > 0) {
                tagInfo.append("=== MESSAGGI NDEF ===\n");
                for (Parcelable rawMessage : rawMessages) {
                    if (rawMessage instanceof NdefMessage) {
                        NdefMessage message = (NdefMessage) rawMessage;
                        tagInfo.append(parseNdefMessage(message)).append("\n");
                    }
                }
            } else {
                // Se non ci sono messaggi NDEF nell'intent, prova a leggere direttamente dal tag
                tagInfo.append(readNdefFromTag(tag));
            }
            
        } catch (Exception e) {
            tagInfo.append("=== ERRORE ===\n");
            tagInfo.append("Errore durante l'elaborazione del tag: ").append(e.getMessage()).append("\n");
            tagInfo.append("Tipo di errore: ").append(e.getClass().getSimpleName()).append("\n");
        }
        
        listener.onNFCDataReceived(tagInfo.toString());
        
        // Mostra un feedback all'utente
        Toast.makeText(activity, "Tag NFC letto con successo!", Toast.LENGTH_SHORT).show();
    }
    
    /**
     * Parsa un messaggio NDEF
     */
    private String parseNdefMessage(NdefMessage message) {
        StringBuilder result = new StringBuilder();
        NdefRecord[] records = message.getRecords();
        
        for (int i = 0; i < records.length; i++) {
            result.append("Record ").append(i + 1).append(":\n");
            result.append(parseNdefRecord(records[i])).append("\n");
        }
        
        return result.toString();
    }
    
    /**
     * Parsa un singolo record NDEF
     */
    private String parseNdefRecord(NdefRecord record) {
        StringBuilder result = new StringBuilder();
        
        // Tipo di record
        String type = new String(record.getType());
        result.append("  Tipo: ").append(type).append("\n");
        
        // TNF (Type Name Format)
        short tnf = record.getTnf();
        result.append("  TNF: ").append(getTnfDescription(tnf)).append("\n");
        
        // Payload
        byte[] payload = record.getPayload();
        result.append("  Payload (hex): ").append(bytesToHex(payload)).append("\n");
        
        // Prova a interpretare il payload come testo
        String textContent = parseTextRecord(record);
        if (textContent != null) {
            result.append("  Contenuto testo: ").append(textContent).append("\n");
        }
        
        // Prova a interpretare come URI
        String uriContent = parseUriRecord(record);
        if (uriContent != null) {
            result.append("  URI: ").append(uriContent).append("\n");
        }
        
        return result.toString();
    }
    
    /**
     * Legge dati NDEF direttamente dal tag
     */
    private String readNdefFromTag(Tag tag) {
        Ndef ndef = Ndef.get(tag);
        if (ndef == null) {
            return "=== DATI RAW ===\nNessun dato NDEF trovato. Tag raw o non formattato.\n";
        }
        
        try {
            ndef.connect();
            NdefMessage message = ndef.getNdefMessage();
            ndef.close();
            
            if (message != null) {
                return "=== MESSAGGI NDEF (letti dal tag) ===\n" + parseNdefMessage(message);
            } else {
                return "=== DATI RAW ===\nTag NDEF vuoto.\n";
            }
        } catch (Exception e) {
            return "=== ERRORE ===\nErrore nella lettura del tag: " + e.getMessage() + "\n";
        }
    }
    
    /**
     * Parsa un record di testo NDEF
     */
    private String parseTextRecord(NdefRecord record) {
        if (record.getTnf() == NdefRecord.TNF_WELL_KNOWN && 
            Arrays.equals(record.getType(), NdefRecord.RTD_TEXT)) {
            
            try {
                byte[] payload = record.getPayload();
                String textEncoding = ((payload[0] & 128) == 0) ? "UTF-8" : "UTF-16";
                int languageCodeLength = payload[0] & 0063;
                return new String(payload, languageCodeLength + 1, 
                                payload.length - languageCodeLength - 1, textEncoding);
            } catch (UnsupportedEncodingException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * Parsa un record URI NDEF
     */
    private String parseUriRecord(NdefRecord record) {
        if (record.getTnf() == NdefRecord.TNF_WELL_KNOWN && 
            Arrays.equals(record.getType(), NdefRecord.RTD_URI)) {
            
            byte[] payload = record.getPayload();
            if (payload.length > 0) {
                String uriPrefix = getUriPrefix(payload[0]);
                String uri = new String(payload, 1, payload.length - 1);
                return uriPrefix + uri;
            }
        }
        return null;
    }
    
    /**
     * Ottiene il prefisso URI dal codice
     */
    private String getUriPrefix(byte prefixCode) {
        switch (prefixCode) {
            case 0x01: return "http://www.";
            case 0x02: return "https://www.";
            case 0x03: return "http://";
            case 0x04: return "https://";
            case 0x05: return "tel:";
            case 0x06: return "mailto:";
            default: return "";
        }
    }
    
    /**
     * Ottiene la descrizione del TNF
     */
    private String getTnfDescription(short tnf) {
        switch (tnf) {
            case NdefRecord.TNF_EMPTY: return "Empty";
            case NdefRecord.TNF_WELL_KNOWN: return "Well Known";
            case NdefRecord.TNF_MIME_MEDIA: return "MIME Media";
            case NdefRecord.TNF_ABSOLUTE_URI: return "Absolute URI";
            case NdefRecord.TNF_EXTERNAL_TYPE: return "External Type";
            case NdefRecord.TNF_UNKNOWN: return "Unknown";
            case NdefRecord.TNF_UNCHANGED: return "Unchanged";
            default: return "Invalid TNF";
        }
    }
    
    /**
     * Converte array di byte in stringa esadecimale
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X ", b));
        }
        return result.toString().trim();
    }
    
    /**
     * Verifica se NFC è disponibile
     */
    public boolean isNFCAvailable() {
        return nfcAdapter != null;
    }
    
    /**
     * Verifica se NFC è abilitato
     */
    public boolean isNFCEnabled() {
        return nfcAdapter != null && nfcAdapter.isEnabled();
    }
}
