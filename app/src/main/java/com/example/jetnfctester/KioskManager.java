package com.example.jetnfctester;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.WindowManager;
import android.widget.Toast;

/**
 * Gestisce la modalità kiosk dell'applicazione
 */
public class KioskManager {

    private Activity activity;
    private boolean isKioskMode = false;
    private Handler kioskHandler;
    private Runnable kioskRunnable;

    // Variabili per tracciare lo stato dell'app
    private boolean isAppInForegroundState = true;
    private long lastForegroundTime = System.currentTimeMillis();

    // Interface per comunicare con l'activity
    public interface KioskListener {
        void onKioskModeChanged(boolean enabled);
        void onRequestAdminCode();
    }

    private KioskListener listener;

    public KioskManager(Activity activity, KioskListener listener) {
        this.activity = activity;
        this.listener = listener;
        this.kioskHandler = new Handler(Looper.getMainLooper());
    }

    /**
     * Inizializza la modalità kiosk (attivazione automatica)
     */
    public void initialize() {
        // Attiva automaticamente la modalità kiosk all'avvio
        enableKioskMode();
    }

    /**
     * Abilita la modalità kiosk
     */
    public void enableKioskMode() {
        isKioskMode = true;

        // Nasconde la barra di navigazione e la status bar
        hideSystemUI();

        // Aggiunge listener per intercettare cambiamenti dell'UI di sistema
        activity.getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(visibility -> {
            if (isKioskMode) {
                // Se l'UI di sistema diventa visibile, nascondila di nuovo
                if ((visibility & View.SYSTEM_UI_FLAG_FULLSCREEN) == 0) {
                    hideSystemUI();
                }
            }
        });

        // Avvia il monitoraggio per riportare l'app in primo piano
        startKioskMonitoring();

        Toast.makeText(activity, "Modalità Kiosk attivata", Toast.LENGTH_SHORT).show();

        if (listener != null) {
            listener.onKioskModeChanged(true);
        }
    }

    /**
     * Disabilita la modalità kiosk
     */
    public void disableKioskMode() {
        // PRIMA cosa: disattiva immediatamente la modalità kiosk
        isKioskMode = false;

        // Ferma TUTTO il monitoraggio immediatamente
        stopKioskMonitoring();

        // Reset completo dello stato dell'app
        isAppInForegroundState = true;
        lastForegroundTime = System.currentTimeMillis();

        // Ripristina immediatamente l'UI normale
        showSystemUI();

        // Forza il ripristino completo della navigazione
        forceRestoreNavigation();

        Toast.makeText(activity, "Modalità Kiosk disattivata - L'app si riavvierà per ripristinare la navigazione normale", Toast.LENGTH_LONG).show();

        if (listener != null) {
            listener.onKioskModeChanged(false);
        }

        // Forza un reset completo dell'activity per assicurarsi che tutto funzioni
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (!isKioskMode) { // Doppio controllo prima di ricreare
                ((MainActivity) activity).recreate();
            }
        }, 1000); // Delay più lungo per dare tempo al toast
    }

    /**
     * Nasconde l'UI di sistema
     */
    private void hideSystemUI() {
        try {
            // Nasconde l'UI di sistema ma permette le gesture
            activity.getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            );

            // Solo i flag essenziali per la modalità kiosk
            activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

            // Impedisce screenshot solo in modalità kiosk
            activity.getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE,
                               WindowManager.LayoutParams.FLAG_SECURE);

        } catch (Exception e) {
            // Continua anche se alcune impostazioni falliscono
            e.printStackTrace();
        }
    }

    /**
     * Mostra l'UI di sistema normale
     */
    private void showSystemUI() {
        try {
            // Ripristina completamente l'UI di sistema normale
            activity.getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            );

            // Forza il ripristino completo dopo un breve delay
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }, 100);

            // Rimuovi tutti i flag della modalità kiosk
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);

            // Rimuovi il listener per i cambiamenti dell'UI di sistema
            activity.getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(null);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Forza il ripristino completo della navigazione Android
     */
    private void forceRestoreNavigation() {
        try {
            // Sequenza di ripristino per assicurarsi che la navigazione funzioni
            Handler handler = new Handler(Looper.getMainLooper());

            // Step 1: Reset immediato
            activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);

            // Step 2: Ripristino dopo 200ms
            handler.postDelayed(() -> {
                try {
                    activity.getWindow().getDecorView().setSystemUiVisibility(
                        View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    );
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }, 200);

            // Step 3: Reset finale dopo 500ms
            handler.postDelayed(() -> {
                try {
                    activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);

                    // Forza il refresh della decorView
                    activity.getWindow().getDecorView().requestLayout();
                    activity.getWindow().getDecorView().invalidate();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }, 500);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Avvia il monitoraggio kiosk
     */
    private void startKioskMonitoring() {
        // Non avviare se la modalità kiosk non è attiva
        if (!isKioskMode) {
            return;
        }

        // Ferma qualsiasi monitoraggio precedente
        stopKioskMonitoring();

        kioskRunnable = new Runnable() {
            @Override
            public void run() {
                // DOPPIO controllo per essere sicuri che siamo ancora in modalità kiosk
                if (isKioskMode) {
                    // Controlla se l'app è ancora in primo piano
                    if (!isAppInForeground()) {
                        // Riporta l'app in primo piano con metodi multipli
                        bringAppToFront();

                        // Riapplica le impostazioni UI dopo un breve ritardo
                        if (kioskHandler != null) {
                            kioskHandler.postDelayed(() -> {
                                if (isKioskMode) { // Triplo controllo
                                    hideSystemUI();
                                }
                            }, 100);
                        }
                    } else {
                        // Anche se l'app è in primo piano, riapplica le impostazioni UI
                        // per contrastare eventuali modifiche del sistema
                        if (isKioskMode) { // Controllo aggiuntivo
                            hideSystemUI();
                        }
                    }

                    // Ripeti il controllo solo se siamo ancora in modalità kiosk
                    if (isKioskMode && kioskHandler != null) {
                        kioskHandler.postDelayed(this, 300);
                    }
                }
            }
        };

        if (kioskHandler != null) {
            kioskHandler.post(kioskRunnable);
        }
    }

    /**
     * Ferma il monitoraggio kiosk
     */
    private void stopKioskMonitoring() {
        if (kioskHandler != null) {
            if (kioskRunnable != null) {
                kioskHandler.removeCallbacks(kioskRunnable);
                kioskRunnable = null; // Imposta a null per evitare riutilizzo
            }
            // Rimuovi tutti i callback pendenti
            kioskHandler.removeCallbacksAndMessages(null);
        }
    }

    /**
     * Verifica se l'app è in primo piano
     */
    private boolean isAppInForeground() {
        // Usa il nostro tracking interno dello stato
        if (!isAppInForegroundState) {
            return false;
        }

        // Controllo aggiuntivo: se è passato troppo tempo dall'ultimo onResume
        long timeSinceLastForeground = System.currentTimeMillis() - lastForegroundTime;
        if (timeSinceLastForeground > 2000) { // 2 secondi
            return false;
        }

        // Controllo tramite ActivityManager per versioni supportate
        ActivityManager activityManager = (ActivityManager) activity.getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager != null) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    // Per Android 5.0+ usiamo il nostro tracking interno
                    // Questo è più affidabile delle API deprecate
                    return isAppInForegroundState;
                } else {
                    // Per versioni precedenti
                    ComponentName topActivity = activityManager.getRunningTasks(1).get(0).topActivity;
                    return topActivity.getPackageName().equals(activity.getPackageName());
                }
            } catch (Exception e) {
                // In caso di errore, assumiamo che l'app non sia in primo piano
                return false;
            }
        }
        return isAppInForegroundState;
    }

    /**
     * Riporta l'app in primo piano
     */
    private void bringAppToFront() {
        try {
            // Metodo 1: Riporta l'activity corrente in primo piano
            Intent intent = new Intent(activity, activity.getClass());
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                           Intent.FLAG_ACTIVITY_REORDER_TO_FRONT |
                           Intent.FLAG_ACTIVITY_SINGLE_TOP |
                           Intent.FLAG_ACTIVITY_CLEAR_TOP);
            activity.startActivity(intent);

            // Metodo 2: Usa ActivityManager per portare il task in primo piano
            ActivityManager activityManager = (ActivityManager) activity.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager != null) {
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        // Per Android 5.0+, usa moveTaskToFront se disponibile
                        activityManager.moveTaskToFront(activity.getTaskId(), ActivityManager.MOVE_TASK_WITH_HOME);
                    }
                } catch (Exception e) {
                    // Ignora errori di sicurezza
                }
            }

            // Metodo 3: Forza il focus sulla finestra
            activity.getWindow().getDecorView().requestFocus();

        } catch (Exception e) {
            // Log dell'errore ma continua
            e.printStackTrace();
        }
    }

    /**
     * Gestisce i cambiamenti di focus della finestra
     */
    public void onWindowFocusChanged(boolean hasFocus) {
        // SOLO se siamo effettivamente in modalità kiosk
        if (isKioskMode) {
            if (hasFocus) {
                // Quando riacquisiamo il focus, riapplica le impostazioni kiosk
                hideSystemUI();
                isAppInForegroundState = true;
                lastForegroundTime = System.currentTimeMillis();
            } else {
                // Quando perdiamo il focus, prova a riportare l'app in primo piano
                isAppInForegroundState = false;
                if (kioskHandler != null) {
                    kioskHandler.postDelayed(() -> {
                        // DOPPIO controllo per essere sicuri
                        if (isKioskMode && !isAppInForeground()) {
                            bringAppToFront();
                        }
                    }, 100);
                }
            }
        } else {
            // Se NON siamo in modalità kiosk, aggiorna solo lo stato senza nascondere UI
            isAppInForegroundState = hasFocus;
            if (hasFocus) {
                lastForegroundTime = System.currentTimeMillis();
                // Assicurati che l'UI sia visibile quando non siamo in modalità kiosk
                showSystemUI();
            }
        }
    }

    /**
     * Gestisce onResume dell'activity
     */
    public void onResume() {
        // Aggiorna lo stato dell'app
        isAppInForegroundState = true;
        lastForegroundTime = System.currentTimeMillis();

        // Riattiva la modalità kiosk SOLO se era effettivamente attiva
        if (isKioskMode) {
            hideSystemUI();
            startKioskMonitoring();
        } else {
            // Se non siamo in modalità kiosk, assicurati che l'UI sia normale
            showSystemUI();
        }
    }

    /**
     * Gestisce onPause dell'activity
     */
    public void onPause() {
        // Aggiorna lo stato dell'app
        isAppInForegroundState = false;

        // Non fermare il monitoraggio kiosk in onPause per mantenere l'app attiva
    }

    /**
     * Gestisce la pressione dei tasti di sistema
     */
    public boolean handleKeyDown(int keyCode) {
        if (isKioskMode) {
            // Blocca solo i tasti essenziali in modalità kiosk
            switch (keyCode) {
                case android.view.KeyEvent.KEYCODE_HOME:
                case android.view.KeyEvent.KEYCODE_RECENT_APPS:
                case android.view.KeyEvent.KEYCODE_MENU:
                case android.view.KeyEvent.KEYCODE_SEARCH:
                case android.view.KeyEvent.KEYCODE_APP_SWITCH:
                    return true; // Blocca il tasto
                case android.view.KeyEvent.KEYCODE_BACK:
                    if (listener != null) {
                        listener.onRequestAdminCode();
                    }
                    return true;
                // Non blocchiamo più POWER, VOLUME, ASSIST per evitare problemi
            }
        }
        return false; // Non gestito
    }

    /**
     * Gestisce il rilascio dei tasti di sistema
     */
    public boolean handleKeyUp(int keyCode) {
        if (isKioskMode) {
            // Blocca solo i tasti essenziali in modalità kiosk
            switch (keyCode) {
                case android.view.KeyEvent.KEYCODE_HOME:
                case android.view.KeyEvent.KEYCODE_RECENT_APPS:
                case android.view.KeyEvent.KEYCODE_MENU:
                case android.view.KeyEvent.KEYCODE_SEARCH:
                case android.view.KeyEvent.KEYCODE_APP_SWITCH:
                case android.view.KeyEvent.KEYCODE_BACK:
                    return true; // Blocca il tasto
            }
        }
        return false; // Non gestito
    }

    /**
     * Gestisce la pressione prolungata dei tasti di sistema
     */
    public boolean handleKeyLongPress(int keyCode) {
        if (isKioskMode) {
            // Blocca solo i tasti essenziali in modalità kiosk
            switch (keyCode) {
                case android.view.KeyEvent.KEYCODE_HOME:
                case android.view.KeyEvent.KEYCODE_RECENT_APPS:
                case android.view.KeyEvent.KEYCODE_MENU:
                case android.view.KeyEvent.KEYCODE_SEARCH:
                case android.view.KeyEvent.KEYCODE_APP_SWITCH:
                case android.view.KeyEvent.KEYCODE_BACK:
                    return true; // Blocca il tasto
            }
        }
        return false; // Non gestito
    }

    /**
     * Pulisce le risorse
     */
    public void cleanup() {
        stopKioskMonitoring();
    }

    /**
     * Verifica se la modalità kiosk è attiva
     */
    public boolean isKioskModeEnabled() {
        return isKioskMode;
    }

    /**
     * Forza il reset completo di tutte le impostazioni (per debug)
     */
    public void forceCompleteReset() {
        // Disabilita tutto
        isKioskMode = false;
        stopKioskMonitoring();

        // Reset dello stato
        isAppInForegroundState = true;
        lastForegroundTime = System.currentTimeMillis();

        // Ripristino UI multiplo per essere sicuri
        try {
            // Reset immediato
            activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
            activity.getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(null);

            // Reset con delay per essere sicuri
            Handler handler = new Handler(Looper.getMainLooper());
            handler.postDelayed(() -> {
                try {
                    activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
                    activity.getWindow().getDecorView().requestLayout();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }, 300);

            Toast.makeText(activity, "Reset completo eseguito - Navigazione ripristinata", Toast.LENGTH_LONG).show();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
