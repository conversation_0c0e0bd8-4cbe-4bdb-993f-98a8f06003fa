package com.example.jetnfctester;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.widget.TextView;

/**
 * Activity di test minimale per verificare la navigazione Android
 */
public class TestActivity extends Activity {
    
    private static final String TAG = "TestActivity";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Layout minimale
        TextView textView = new TextView(this);
        textView.setText("TEST ACTIVITY\n\nPremi i pulsanti di navigazione per testare:\n- Home\n- Recent Apps\n- Back\n\nGuarda i log in Android Studio per vedere se vengono rilevati.");
        textView.setPadding(50, 50, 50, 50);
        textView.setTextSize(16);
        
        setContentView(textView);
        
        Log.i(TAG, "TestActivity created - Navigation should work normally");
    }
    
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        String keyName = KeyEvent.keyCodeToString(keyCode);
        Log.i(TAG, "KEY DOWN: " + keyName + " (keyCode: " + keyCode + ")");
        
        // NON bloccare nessun tasto - lascia che Android gestisca tutto
        return super.onKeyDown(keyCode, event);
    }
    
    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        String keyName = KeyEvent.keyCodeToString(keyCode);
        Log.i(TAG, "KEY UP: " + keyName + " (keyCode: " + keyCode + ")");
        
        // NON bloccare nessun tasto
        return super.onKeyUp(keyCode, event);
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        Log.i(TAG, "TestActivity resumed");
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        Log.i(TAG, "TestActivity paused");
    }
}
