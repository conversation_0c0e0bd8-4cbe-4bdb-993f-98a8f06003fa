package com.example.jetnfctester;

import android.app.PendingIntent;
import android.content.Intent;
import android.content.IntentFilter;
import android.nfc.NdefMessage;
import android.nfc.NdefRecord;
import android.nfc.NfcAdapter;
import android.nfc.Tag;
import android.nfc.tech.Ndef;
import android.nfc.tech.NdefFormatable;
import android.os.Bundle;
import android.os.Parcelable;
import android.text.TextWatcher;
import android.text.Editable;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;

public class MainActivity extends AppCompatActivity {

    private NfcAdapter nfcAdapter;
    private PendingIntent pendingIntent;
    private IntentFilter[] intentFiltersArray;
    private String[][] techListsArray;

    private TextView statusTextView;
    private TextView nfcDataTextView;
    private Spinner writeTypeSpinner;
    private EditText writeDataEditText;
    private Button writeButton;
    private Button clearButton;

    private boolean isWriteMode = false;
    private Tag pendingWriteTag = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        // Inizializza le view
        statusTextView = findViewById(R.id.statusTextView);
        nfcDataTextView = findViewById(R.id.nfcDataTextView);
        writeTypeSpinner = findViewById(R.id.writeTypeSpinner);
        writeDataEditText = findViewById(R.id.writeDataEditText);
        writeButton = findViewById(R.id.writeButton);
        clearButton = findViewById(R.id.clearButton);

        // Configura lo spinner per i tipi di scrittura
        setupWriteTypeSpinner();

        // Configura i listener per i pulsanti
        setupButtonListeners();

        // Imposta l'hint iniziale
        updateHintForSelectedType();

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Inizializza NFC
        initializeNFC();

        // Gestisci l'intent se l'app è stata aperta da un tag NFC
        handleIntent(getIntent());
    }

    private void setupWriteTypeSpinner() {
        String[] writeTypes = {"Testo", "URL", "Email", "Telefono"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, writeTypes);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        writeTypeSpinner.setAdapter(adapter);

        // Listener per cambiare il hint dell'EditText in base al tipo selezionato
        writeTypeSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                updateHintForSelectedType();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
    }

    private void updateHintForSelectedType() {
        String selectedType = writeTypeSpinner.getSelectedItem().toString();
        switch (selectedType) {
            case "Testo":
                writeDataEditText.setHint("Inserisci il testo da scrivere...");
                break;
            case "URL":
                writeDataEditText.setHint("Inserisci l'URL (es: https://www.example.com)");
                break;
            case "Email":
                writeDataEditText.setHint("Inserisci l'indirizzo email (es: <EMAIL>)");
                break;
            case "Telefono":
                writeDataEditText.setHint("Inserisci il numero di telefono (es: +39 ************)");
                break;
        }
    }

    private void setupButtonListeners() {
        writeButton.setOnClickListener(v -> {
            String data = writeDataEditText.getText().toString().trim();
            String selectedType = writeTypeSpinner.getSelectedItem().toString();

            // Validazione input
            String validationError = validateInput(selectedType, data);
            if (validationError != null) {
                Toast.makeText(this, validationError, Toast.LENGTH_LONG).show();
                return;
            }

            isWriteMode = true;
            statusTextView.setText("Modalità scrittura attiva - Avvicina un tag NFC");
            statusTextView.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));

            // Disabilita i controlli durante la modalità scrittura
            writeTypeSpinner.setEnabled(false);
            writeDataEditText.setEnabled(false);
            writeButton.setEnabled(false);

            Toast.makeText(this, "Avvicina un tag NFC per scrivere i dati", Toast.LENGTH_LONG).show();
        });

        clearButton.setOnClickListener(v -> {
            writeDataEditText.setText("");
            resetWriteMode();
        });
    }

    private String validateInput(String type, String data) {
        if (data.isEmpty()) {
            return "Inserisci del testo da scrivere";
        }

        switch (type) {
            case "URL":
                if (!data.startsWith("http://") && !data.startsWith("https://")) {
                    return "L'URL deve iniziare con http:// o https://";
                }
                break;
            case "Email":
                if (!data.contains("@") || !data.contains(".")) {
                    return "Inserisci un indirizzo email valido";
                }
                break;
            case "Telefono":
                // Rimuovi spazi e caratteri speciali per la validazione
                String cleanPhone = data.replaceAll("[\\s\\-\\(\\)\\+]", "");
                if (cleanPhone.length() < 6 || !cleanPhone.matches("\\d+")) {
                    return "Inserisci un numero di telefono valido";
                }
                break;
            case "Testo":
                if (data.length() > 1000) {
                    return "Il testo è troppo lungo (massimo 1000 caratteri)";
                }
                break;
        }

        return null; // Input valido
    }

    private void resetWriteMode() {
        isWriteMode = false;
        pendingWriteTag = null;

        // Riabilita i controlli
        writeTypeSpinner.setEnabled(true);
        writeDataEditText.setEnabled(true);
        writeButton.setEnabled(true);

        // Reset colore status
        statusTextView.setTextColor(getResources().getColor(android.R.color.darker_gray));
        statusTextView.setText("NFC attivo - In attesa di tag...");
    }

    private void initializeNFC() {
        nfcAdapter = NfcAdapter.getDefaultAdapter(this);

        if (nfcAdapter == null) {
            statusTextView.setText("NFC non supportato su questo dispositivo");
            nfcDataTextView.setText("Questo dispositivo non supporta la tecnologia NFC.");
            return;
        }

        if (!nfcAdapter.isEnabled()) {
            statusTextView.setText("NFC disabilitato - Abilitalo nelle impostazioni");
            nfcDataTextView.setText("Per utilizzare questa app, abilita NFC nelle impostazioni del dispositivo.");
            return;
        }

        statusTextView.setText("NFC attivo - In attesa di tag...");

        // Configura il PendingIntent per il foreground dispatch
        Intent intent = new Intent(this, getClass()).addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
        pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_MUTABLE);

        // Configura i filtri per intercettare tutti i tipi di tag NFC
        IntentFilter ndef = new IntentFilter(NfcAdapter.ACTION_NDEF_DISCOVERED);
        IntentFilter tag = new IntentFilter(NfcAdapter.ACTION_TAG_DISCOVERED);
        IntentFilter tech = new IntentFilter(NfcAdapter.ACTION_TECH_DISCOVERED);

        intentFiltersArray = new IntentFilter[]{ndef, tag, tech};

        // Lista delle tecnologie supportate
        techListsArray = new String[][]{
            new String[]{Ndef.class.getName()},
            new String[]{NdefFormatable.class.getName()}
        };
    }

    @Override
    protected void onResume() {
        super.onResume();
        checkNfcStatus();
        if (nfcAdapter != null && nfcAdapter.isEnabled()) {
            nfcAdapter.enableForegroundDispatch(this, pendingIntent, intentFiltersArray, techListsArray);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (nfcAdapter != null) {
            nfcAdapter.disableForegroundDispatch(this);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        handleIntent(intent);
    }

    private void checkNfcStatus() {
        if (nfcAdapter == null) {
            statusTextView.setText("NFC non supportato su questo dispositivo");
            nfcDataTextView.setText("Questo dispositivo non supporta la tecnologia NFC.\n\nPer utilizzare questa app è necessario un dispositivo con supporto NFC.");
            return;
        }

        if (!nfcAdapter.isEnabled()) {
            statusTextView.setText("NFC disabilitato - Abilitalo nelle impostazioni");
            nfcDataTextView.setText("NFC è disabilitato su questo dispositivo.\n\nPer utilizzare questa app:\n1. Vai nelle Impostazioni del dispositivo\n2. Cerca 'NFC' o 'Connessioni'\n3. Abilita NFC\n4. Torna a questa app");

            // Mostra un toast per guidare l'utente
            Toast.makeText(this, "Abilita NFC nelle impostazioni per utilizzare l'app", Toast.LENGTH_LONG).show();
            return;
        }

        statusTextView.setText("NFC attivo - In attesa di tag...");
        if (nfcDataTextView.getText().toString().contains("Questo dispositivo non supporta") ||
            nfcDataTextView.getText().toString().contains("NFC è disabilitato")) {
            nfcDataTextView.setText("Avvicina un tag NFC al dispositivo per vedere le informazioni...");
        }
    }

    private void handleIntent(Intent intent) {
        if (intent == null) return;

        String action = intent.getAction();

        if (NfcAdapter.ACTION_NDEF_DISCOVERED.equals(action) ||
            NfcAdapter.ACTION_TAG_DISCOVERED.equals(action) ||
            NfcAdapter.ACTION_TECH_DISCOVERED.equals(action)) {

            Tag tag = intent.getParcelableExtra(NfcAdapter.EXTRA_TAG);
            if (tag != null) {
                try {
                    if (isWriteMode) {
                        // Modalità scrittura
                        writeToNfcTag(tag);
                    } else {
                        // Modalità lettura
                        processNfcTag(tag, intent);
                    }
                } catch (Exception e) {
                    statusTextView.setText("Errore nell'operazione NFC");
                    nfcDataTextView.setText("=== ERRORE ===\nSi è verificato un errore durante l'operazione NFC:\n" +
                                          e.getMessage() + "\n\nProva ad avvicinare nuovamente il tag.");
                }
            } else {
                statusTextView.setText("Errore: tag non valido");
                nfcDataTextView.setText("=== ERRORE ===\nIl tag NFC rilevato non è valido o non può essere letto.");
            }
        }
    }

    private void processNfcTag(Tag tag, Intent intent) {
        statusTextView.setText("Tag NFC rilevato!");

        StringBuilder tagInfo = new StringBuilder();
        tagInfo.append("=== INFORMAZIONI TAG NFC ===\n\n");

        try {
            // Informazioni di base del tag
            byte[] tagId = tag.getId();
            if (tagId != null && tagId.length > 0) {
                tagInfo.append("ID Tag: ").append(bytesToHex(tagId)).append("\n");
            } else {
                tagInfo.append("ID Tag: Non disponibile\n");
            }

            String[] techList = tag.getTechList();
            if (techList != null && techList.length > 0) {
                tagInfo.append("Tecnologie supportate: ").append(Arrays.toString(techList)).append("\n\n");
            } else {
                tagInfo.append("Tecnologie supportate: Nessuna rilevata\n\n");
            }

            // Prova a leggere i dati NDEF
            Parcelable[] rawMessages = intent.getParcelableArrayExtra(NfcAdapter.EXTRA_NDEF_MESSAGES);
            if (rawMessages != null && rawMessages.length > 0) {
                tagInfo.append("=== MESSAGGI NDEF ===\n");
                for (Parcelable rawMessage : rawMessages) {
                    if (rawMessage instanceof NdefMessage) {
                        NdefMessage message = (NdefMessage) rawMessage;
                        tagInfo.append(parseNdefMessage(message)).append("\n");
                    }
                }
            } else {
                // Se non ci sono messaggi NDEF nell'intent, prova a leggere direttamente dal tag
                tagInfo.append(readNdefFromTag(tag));
            }

        } catch (Exception e) {
            tagInfo.append("=== ERRORE ===\n");
            tagInfo.append("Errore durante l'elaborazione del tag: ").append(e.getMessage()).append("\n");
            tagInfo.append("Tipo di errore: ").append(e.getClass().getSimpleName()).append("\n");
        }

        nfcDataTextView.setText(tagInfo.toString());

        // Mostra un feedback all'utente
        Toast.makeText(this, "Tag NFC letto con successo!", Toast.LENGTH_SHORT).show();
    }

    private String parseNdefMessage(NdefMessage message) {
        StringBuilder result = new StringBuilder();
        NdefRecord[] records = message.getRecords();

        for (int i = 0; i < records.length; i++) {
            result.append("Record ").append(i + 1).append(":\n");
            result.append(parseNdefRecord(records[i])).append("\n");
        }

        return result.toString();
    }

    private String parseNdefRecord(NdefRecord record) {
        StringBuilder result = new StringBuilder();

        // Tipo di record
        String type = new String(record.getType());
        result.append("  Tipo: ").append(type).append("\n");

        // TNF (Type Name Format)
        short tnf = record.getTnf();
        result.append("  TNF: ").append(getTnfDescription(tnf)).append("\n");

        // Payload
        byte[] payload = record.getPayload();
        result.append("  Payload (hex): ").append(bytesToHex(payload)).append("\n");

        // Prova a interpretare il payload come testo
        String textContent = parseTextRecord(record);
        if (textContent != null) {
            result.append("  Contenuto testo: ").append(textContent).append("\n");
        }

        // Prova a interpretare come URI
        String uriContent = parseUriRecord(record);
        if (uriContent != null) {
            result.append("  URI: ").append(uriContent).append("\n");
        }

        return result.toString();
    }

    private String readNdefFromTag(Tag tag) {
        Ndef ndef = Ndef.get(tag);
        if (ndef == null) {
            return "=== DATI RAW ===\nNessun dato NDEF trovato. Tag raw o non formattato.\n";
        }

        try {
            ndef.connect();
            NdefMessage message = ndef.getNdefMessage();
            ndef.close();

            if (message != null) {
                return "=== MESSAGGI NDEF (letti dal tag) ===\n" + parseNdefMessage(message);
            } else {
                return "=== DATI RAW ===\nTag NDEF vuoto.\n";
            }
        } catch (Exception e) {
            return "=== ERRORE ===\nErrore nella lettura del tag: " + e.getMessage() + "\n";
        }
    }

    private String parseTextRecord(NdefRecord record) {
        if (record.getTnf() == NdefRecord.TNF_WELL_KNOWN &&
            Arrays.equals(record.getType(), NdefRecord.RTD_TEXT)) {

            try {
                byte[] payload = record.getPayload();
                String textEncoding = ((payload[0] & 128) == 0) ? "UTF-8" : "UTF-16";
                int languageCodeLength = payload[0] & 0063;
                return new String(payload, languageCodeLength + 1,
                                payload.length - languageCodeLength - 1, textEncoding);
            } catch (UnsupportedEncodingException e) {
                return null;
            }
        }
        return null;
    }

    private String parseUriRecord(NdefRecord record) {
        if (record.getTnf() == NdefRecord.TNF_WELL_KNOWN &&
            Arrays.equals(record.getType(), NdefRecord.RTD_URI)) {

            byte[] payload = record.getPayload();
            if (payload.length > 0) {
                String uriPrefix = getUriPrefix(payload[0]);
                String uri = new String(payload, 1, payload.length - 1);
                return uriPrefix + uri;
            }
        }
        return null;
    }

    private String getUriPrefix(byte prefixCode) {
        switch (prefixCode) {
            case 0x01: return "http://www.";
            case 0x02: return "https://www.";
            case 0x03: return "http://";
            case 0x04: return "https://";
            case 0x05: return "tel:";
            case 0x06: return "mailto:";
            default: return "";
        }
    }

    private String getTnfDescription(short tnf) {
        switch (tnf) {
            case NdefRecord.TNF_EMPTY: return "Empty";
            case NdefRecord.TNF_WELL_KNOWN: return "Well Known";
            case NdefRecord.TNF_MIME_MEDIA: return "MIME Media";
            case NdefRecord.TNF_ABSOLUTE_URI: return "Absolute URI";
            case NdefRecord.TNF_EXTERNAL_TYPE: return "External Type";
            case NdefRecord.TNF_UNKNOWN: return "Unknown";
            case NdefRecord.TNF_UNCHANGED: return "Unchanged";
            default: return "Invalid TNF";
        }
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X ", b));
        }
        return result.toString().trim();
    }

    private void writeToNfcTag(Tag tag) {
        try {
            // Mostra feedback di scrittura in corso
            statusTextView.setText("Scrittura in corso...");
            statusTextView.setTextColor(getResources().getColor(android.R.color.holo_blue_bright));

            String data = writeDataEditText.getText().toString().trim();
            String selectedType = writeTypeSpinner.getSelectedItem().toString();

            // Crea il record NDEF basato sul tipo selezionato
            NdefRecord record = createNdefRecord(selectedType, data);
            if (record == null) {
                statusTextView.setText("Errore: formato dati non valido");
                statusTextView.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                Toast.makeText(this, "Formato dati non valido per il tipo selezionato", Toast.LENGTH_LONG).show();
                resetWriteMode();
                return;
            }

            // Crea il messaggio NDEF
            NdefMessage message = new NdefMessage(record);

            // Prova a scrivere sul tag
            boolean success = writeNdefMessage(tag, message);

            if (success) {
                statusTextView.setText("Scrittura completata con successo!");
                statusTextView.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
                nfcDataTextView.setText("=== SCRITTURA COMPLETATA ===\n\n" +
                    "Tipo: " + selectedType + "\n" +
                    "Dati scritti: " + data + "\n\n" +
                    "Il tag è stato aggiornato con successo.\n\n" +
                    "Puoi ora leggere il tag per verificare i dati scritti.");
                Toast.makeText(this, "Dati scritti con successo sul tag NFC!", Toast.LENGTH_LONG).show();

                // Reset modalità scrittura
                resetWriteMode();
                writeDataEditText.setText("");
            } else {
                statusTextView.setText("Errore durante la scrittura");
                statusTextView.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                nfcDataTextView.setText("=== ERRORE SCRITTURA ===\n\n" +
                    "Non è stato possibile scrivere sul tag.\n\n" +
                    "Possibili cause:\n" +
                    "• Tag di sola lettura\n" +
                    "• Tag non compatibile\n" +
                    "• Spazio insufficiente\n" +
                    "• Tag danneggiato\n\n" +
                    "Prova con un altro tag o verifica che il tag sia scrivibile.");
                Toast.makeText(this, "Impossibile scrivere sul tag", Toast.LENGTH_LONG).show();
                resetWriteMode();
            }

        } catch (Exception e) {
            statusTextView.setText("Errore durante la scrittura");
            statusTextView.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            nfcDataTextView.setText("=== ERRORE ===\n" +
                "Errore durante la scrittura: " + e.getMessage() + "\n\n" +
                "Riprova con un altro tag.");
            Toast.makeText(this, "Errore: " + e.getMessage(), Toast.LENGTH_LONG).show();
            resetWriteMode();
        }
    }

    private NdefRecord createNdefRecord(String type, String data) {
        try {
            switch (type) {
                case "Testo":
                    return createTextRecord(data);
                case "URL":
                    return createUriRecord(data);
                case "Email":
                    return createUriRecord("mailto:" + data);
                case "Telefono":
                    return createUriRecord("tel:" + data);
                default:
                    return createTextRecord(data);
            }
        } catch (Exception e) {
            return null;
        }
    }

    private NdefRecord createTextRecord(String text) {
        try {
            String lang = "it";
            byte[] textBytes = text.getBytes("UTF-8");
            byte[] langBytes = lang.getBytes("US-ASCII");
            int langLength = langBytes.length;
            int textLength = textBytes.length;
            byte[] payload = new byte[1 + langLength + textLength];

            // Imposta il flag di codifica (0 = UTF-8)
            payload[0] = (byte) langLength;

            // Copia i byte della lingua
            System.arraycopy(langBytes, 0, payload, 1, langLength);

            // Copia i byte del testo
            System.arraycopy(textBytes, 0, payload, 1 + langLength, textLength);

            return new NdefRecord(NdefRecord.TNF_WELL_KNOWN, NdefRecord.RTD_TEXT, new byte[0], payload);
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    private NdefRecord createUriRecord(String uri) {
        try {
            byte uriPrefix = 0x00; // Nessun prefisso

            // Controlla i prefissi comuni
            if (uri.startsWith("http://www.")) {
                uriPrefix = 0x01;
                uri = uri.substring(11);
            } else if (uri.startsWith("https://www.")) {
                uriPrefix = 0x02;
                uri = uri.substring(12);
            } else if (uri.startsWith("http://")) {
                uriPrefix = 0x03;
                uri = uri.substring(7);
            } else if (uri.startsWith("https://")) {
                uriPrefix = 0x04;
                uri = uri.substring(8);
            } else if (uri.startsWith("tel:")) {
                uriPrefix = 0x05;
                uri = uri.substring(4);
            } else if (uri.startsWith("mailto:")) {
                uriPrefix = 0x06;
                uri = uri.substring(7);
            }

            byte[] uriBytes = uri.getBytes("UTF-8");
            byte[] payload = new byte[1 + uriBytes.length];
            payload[0] = uriPrefix;
            System.arraycopy(uriBytes, 0, payload, 1, uriBytes.length);

            return new NdefRecord(NdefRecord.TNF_WELL_KNOWN, NdefRecord.RTD_URI, new byte[0], payload);
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    private boolean writeNdefMessage(Tag tag, NdefMessage message) {
        try {
            Ndef ndef = Ndef.get(tag);

            if (ndef != null) {
                // Tag già formattato NDEF
                ndef.connect();

                if (!ndef.isWritable()) {
                    ndef.close();
                    return false;
                }

                int size = message.toByteArray().length;
                if (size > ndef.getMaxSize()) {
                    ndef.close();
                    return false;
                }

                ndef.writeNdefMessage(message);
                ndef.close();
                return true;

            } else {
                // Tag non formattato, prova a formattarlo
                NdefFormatable format = NdefFormatable.get(tag);
                if (format != null) {
                    format.connect();
                    format.format(message);
                    format.close();
                    return true;
                }
            }

        } catch (Exception e) {
            // Log dell'errore per debug
            e.printStackTrace();
        }

        return false;
    }
}