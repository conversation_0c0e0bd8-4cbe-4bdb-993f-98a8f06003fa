package com.example.jetnfctester;

import android.app.PendingIntent;
import android.content.Intent;
import android.content.IntentFilter;
import android.nfc.NdefMessage;
import android.nfc.NdefRecord;
import android.nfc.NfcAdapter;
import android.nfc.Tag;
import android.nfc.tech.Ndef;

import android.os.Bundle;
import android.os.Parcelable;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.content.ComponentName;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;

import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;

public class MainActivity extends AppCompatActivity {

    private NfcAdapter nfcAdapter;
    private PendingIntent pendingIntent;
    private IntentFilter[] intentFiltersArray;
    private String[][] techListsArray;

    private TextView statusTextView;
    private TextView nfcDataTextView;

    // Variabili per modalità kiosk
    private boolean isKioskMode = false;
    private static final String ADMIN_CODE = "1234"; // Codice admin (da cambiare in produzione)
    private Handler kioskHandler;
    private Runnable kioskRunnable;
    private int failedAttempts = 0;
    private static final int MAX_FAILED_ATTEMPTS = 3;
    private int adminButtonTaps = 0;
    private static final int ADMIN_TAPS_REQUIRED = 5;
    private Handler adminTapHandler = new Handler(Looper.getMainLooper());
    private Runnable resetAdminTapsRunnable;

    // Variabili per tracciare lo stato dell'app
    private boolean isAppInForegroundState = true;
    private long lastForegroundTime = System.currentTimeMillis();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        // Inizializza le view
        statusTextView = findViewById(R.id.statusTextView);
        nfcDataTextView = findViewById(R.id.nfcDataTextView);

        // Inizializza il pulsante admin nascosto
        View adminButton = findViewById(R.id.adminButton);
        setupAdminButton(adminButton);

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Inizializza NFC
        initializeNFC();

        // Inizializza modalità kiosk
        initializeKioskMode();

        // Gestisci l'intent se l'app è stata aperta da un tag NFC
        handleIntent(getIntent());
    }



    private void setupAdminButton(View adminButton) {
        adminButton.setOnClickListener(v -> {
            adminButtonTaps++;

            // Reset del contatore dopo 3 secondi se non si raggiunge il numero richiesto
            if (resetAdminTapsRunnable != null) {
                adminTapHandler.removeCallbacks(resetAdminTapsRunnable);
            }

            resetAdminTapsRunnable = () -> {
                adminButtonTaps = 0;
            };
            adminTapHandler.postDelayed(resetAdminTapsRunnable, 3000);

            if (adminButtonTaps >= ADMIN_TAPS_REQUIRED) {
                adminButtonTaps = 0;
                adminTapHandler.removeCallbacks(resetAdminTapsRunnable);
                showAdminMenu();
            } else {
                // Feedback visivo discreto
                if (adminButtonTaps == 1) {
                    Toast.makeText(this, "Admin mode: " + adminButtonTaps + "/" + ADMIN_TAPS_REQUIRED,
                                  Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    private void showAdminMenu() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Menu Amministratore");

        String[] options = {
            isKioskMode ? "Disattiva Modalità Kiosk" : "Attiva Modalità Kiosk",
            "Cambia Codice Admin",
            "Info App"
        };

        builder.setItems(options, (dialog, which) -> {
            switch (which) {
                case 0:
                    if (isKioskMode) {
                        showAdminCodeDialog();
                    } else {
                        enableKioskMode();
                    }
                    break;
                case 1:
                    showChangeAdminCodeDialog();
                    break;
                case 2:
                    showAppInfo();
                    break;
            }
        });

        builder.setNegativeButton("Annulla", null);
        builder.show();
    }

    private void showChangeAdminCodeDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Cambia Codice Admin");
        builder.setMessage("Funzionalità non implementata in questa versione demo.\n" +
                          "Il codice attuale è: " + ADMIN_CODE);
        builder.setPositiveButton("OK", null);
        builder.show();
    }

    private void showAppInfo() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Info App");
        builder.setMessage("NFC Tester v1.0\n\n" +
                          "Modalità Kiosk: " + (isKioskMode ? "ATTIVA" : "DISATTIVA") + "\n" +
                          "Codice Admin: " + ADMIN_CODE + "\n\n" +
                          "Funzionalità:\n" +
                          "• Lettura tag NFC\n" +
                          "• Scrittura tag NFC\n" +
                          "• Modalità Kiosk\n" +
                          "• Controllo admin\n\n" +
                          "Per accedere al menu admin:\n" +
                          "Tocca 5 volte l'angolo in alto a destra");
        builder.setPositiveButton("OK", null);
        builder.show();
    }



    private void initializeNFC() {
        nfcAdapter = NfcAdapter.getDefaultAdapter(this);

        if (nfcAdapter == null) {
            statusTextView.setText("NFC non supportato su questo dispositivo");
            nfcDataTextView.setText("Questo dispositivo non supporta la tecnologia NFC.");
            return;
        }

        if (!nfcAdapter.isEnabled()) {
            statusTextView.setText("NFC disabilitato - Abilitalo nelle impostazioni");
            nfcDataTextView.setText("Per utilizzare questa app, abilita NFC nelle impostazioni del dispositivo.");
            return;
        }

        statusTextView.setText("NFC attivo - In attesa di tag...");

        // Configura il PendingIntent per il foreground dispatch
        Intent intent = new Intent(this, getClass()).addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
        pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_MUTABLE);

        // Configura i filtri per intercettare tutti i tipi di tag NFC
        IntentFilter ndef = new IntentFilter(NfcAdapter.ACTION_NDEF_DISCOVERED);
        IntentFilter tag = new IntentFilter(NfcAdapter.ACTION_TAG_DISCOVERED);
        IntentFilter tech = new IntentFilter(NfcAdapter.ACTION_TECH_DISCOVERED);

        intentFiltersArray = new IntentFilter[]{ndef, tag, tech};

        // Lista delle tecnologie supportate
        techListsArray = new String[][]{
            new String[]{Ndef.class.getName()}
        };
    }

    @Override
    protected void onResume() {
        super.onResume();

        // Aggiorna lo stato dell'app
        isAppInForegroundState = true;
        lastForegroundTime = System.currentTimeMillis();

        checkNfcStatus();
        if (nfcAdapter != null && nfcAdapter.isEnabled()) {
            nfcAdapter.enableForegroundDispatch(this, pendingIntent, intentFiltersArray, techListsArray);
        }

        // Riattiva la modalità kiosk se era attiva
        if (isKioskMode) {
            hideSystemUI();
            startKioskMonitoring();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        // Aggiorna lo stato dell'app
        isAppInForegroundState = false;

        if (nfcAdapter != null) {
            nfcAdapter.disableForegroundDispatch(this);
        }

        // Non fermare il monitoraggio kiosk in onPause per mantenere l'app attiva
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        handleIntent(intent);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Pulisci le risorse della modalità kiosk
        stopKioskMonitoring();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);

        // SOLO se siamo effettivamente in modalità kiosk
        if (isKioskMode) {
            if (hasFocus) {
                // Quando riacquisiamo il focus, riapplica le impostazioni kiosk
                hideSystemUI();
                isAppInForegroundState = true;
                lastForegroundTime = System.currentTimeMillis();
            } else {
                // Quando perdiamo il focus, prova a riportare l'app in primo piano
                isAppInForegroundState = false;
                if (kioskHandler != null) {
                    kioskHandler.postDelayed(() -> {
                        // DOPPIO controllo per essere sicuri
                        if (isKioskMode && !isAppInForeground()) {
                            bringAppToFront();
                        }
                    }, 100);
                }
            }
        } else {
            // Se NON siamo in modalità kiosk, aggiorna solo lo stato senza nascondere UI
            isAppInForegroundState = hasFocus;
            if (hasFocus) {
                lastForegroundTime = System.currentTimeMillis();
                // Assicurati che l'UI sia visibile quando non siamo in modalità kiosk
                showSystemUI();
            }
        }
    }

    private void checkNfcStatus() {
        if (nfcAdapter == null) {
            statusTextView.setText("NFC non supportato su questo dispositivo");
            nfcDataTextView.setText("Questo dispositivo non supporta la tecnologia NFC.\n\nPer utilizzare questa app è necessario un dispositivo con supporto NFC.");
            return;
        }

        if (!nfcAdapter.isEnabled()) {
            statusTextView.setText("NFC disabilitato - Abilitalo nelle impostazioni");
            nfcDataTextView.setText("NFC è disabilitato su questo dispositivo.\n\nPer utilizzare questa app:\n1. Vai nelle Impostazioni del dispositivo\n2. Cerca 'NFC' o 'Connessioni'\n3. Abilita NFC\n4. Torna a questa app");

            // Mostra un toast per guidare l'utente
            Toast.makeText(this, "Abilita NFC nelle impostazioni per utilizzare l'app", Toast.LENGTH_LONG).show();
            return;
        }

        statusTextView.setText("NFC attivo - In attesa di tag...");
        if (nfcDataTextView.getText().toString().contains("Questo dispositivo non supporta") ||
            nfcDataTextView.getText().toString().contains("NFC è disabilitato")) {
            nfcDataTextView.setText("Avvicina un tag NFC al dispositivo per vedere le informazioni...");
        }
    }

    private void handleIntent(Intent intent) {
        if (intent == null) return;

        String action = intent.getAction();

        if (NfcAdapter.ACTION_NDEF_DISCOVERED.equals(action) ||
            NfcAdapter.ACTION_TAG_DISCOVERED.equals(action) ||
            NfcAdapter.ACTION_TECH_DISCOVERED.equals(action)) {

            Tag tag = intent.getParcelableExtra(NfcAdapter.EXTRA_TAG);
            if (tag != null) {
                try {
                    // Solo modalità lettura
                    processNfcTag(tag, intent);
                } catch (Exception e) {
                    statusTextView.setText("Errore nella lettura del tag");
                    nfcDataTextView.setText("=== ERRORE ===\nSi è verificato un errore durante la lettura del tag NFC:\n" +
                                          e.getMessage() + "\n\nProva ad avvicinare nuovamente il tag.");
                }
            } else {
                statusTextView.setText("Errore: tag non valido");
                nfcDataTextView.setText("=== ERRORE ===\nIl tag NFC rilevato non è valido o non può essere letto.");
            }
        }
    }

    private void processNfcTag(Tag tag, Intent intent) {
        statusTextView.setText("Tag NFC rilevato!");

        StringBuilder tagInfo = new StringBuilder();
        tagInfo.append("=== INFORMAZIONI TAG NFC ===\n\n");

        try {
            // Informazioni di base del tag
            byte[] tagId = tag.getId();
            if (tagId != null && tagId.length > 0) {
                tagInfo.append("ID Tag: ").append(bytesToHex(tagId)).append("\n");
            } else {
                tagInfo.append("ID Tag: Non disponibile\n");
            }

            String[] techList = tag.getTechList();
            if (techList != null && techList.length > 0) {
                tagInfo.append("Tecnologie supportate: ").append(Arrays.toString(techList)).append("\n\n");
            } else {
                tagInfo.append("Tecnologie supportate: Nessuna rilevata\n\n");
            }

            // Prova a leggere i dati NDEF
            Parcelable[] rawMessages = intent.getParcelableArrayExtra(NfcAdapter.EXTRA_NDEF_MESSAGES);
            if (rawMessages != null && rawMessages.length > 0) {
                tagInfo.append("=== MESSAGGI NDEF ===\n");
                for (Parcelable rawMessage : rawMessages) {
                    if (rawMessage instanceof NdefMessage) {
                        NdefMessage message = (NdefMessage) rawMessage;
                        tagInfo.append(parseNdefMessage(message)).append("\n");
                    }
                }
            } else {
                // Se non ci sono messaggi NDEF nell'intent, prova a leggere direttamente dal tag
                tagInfo.append(readNdefFromTag(tag));
            }

        } catch (Exception e) {
            tagInfo.append("=== ERRORE ===\n");
            tagInfo.append("Errore durante l'elaborazione del tag: ").append(e.getMessage()).append("\n");
            tagInfo.append("Tipo di errore: ").append(e.getClass().getSimpleName()).append("\n");
        }

        nfcDataTextView.setText(tagInfo.toString());

        // Mostra un feedback all'utente
        Toast.makeText(this, "Tag NFC letto con successo!", Toast.LENGTH_SHORT).show();
    }

    private String parseNdefMessage(NdefMessage message) {
        StringBuilder result = new StringBuilder();
        NdefRecord[] records = message.getRecords();

        for (int i = 0; i < records.length; i++) {
            result.append("Record ").append(i + 1).append(":\n");
            result.append(parseNdefRecord(records[i])).append("\n");
        }

        return result.toString();
    }

    private String parseNdefRecord(NdefRecord record) {
        StringBuilder result = new StringBuilder();

        // Tipo di record
        String type = new String(record.getType());
        result.append("  Tipo: ").append(type).append("\n");

        // TNF (Type Name Format)
        short tnf = record.getTnf();
        result.append("  TNF: ").append(getTnfDescription(tnf)).append("\n");

        // Payload
        byte[] payload = record.getPayload();
        result.append("  Payload (hex): ").append(bytesToHex(payload)).append("\n");

        // Prova a interpretare il payload come testo
        String textContent = parseTextRecord(record);
        if (textContent != null) {
            result.append("  Contenuto testo: ").append(textContent).append("\n");
        }

        // Prova a interpretare come URI
        String uriContent = parseUriRecord(record);
        if (uriContent != null) {
            result.append("  URI: ").append(uriContent).append("\n");
        }

        return result.toString();
    }

    private String readNdefFromTag(Tag tag) {
        Ndef ndef = Ndef.get(tag);
        if (ndef == null) {
            return "=== DATI RAW ===\nNessun dato NDEF trovato. Tag raw o non formattato.\n";
        }

        try {
            ndef.connect();
            NdefMessage message = ndef.getNdefMessage();
            ndef.close();

            if (message != null) {
                return "=== MESSAGGI NDEF (letti dal tag) ===\n" + parseNdefMessage(message);
            } else {
                return "=== DATI RAW ===\nTag NDEF vuoto.\n";
            }
        } catch (Exception e) {
            return "=== ERRORE ===\nErrore nella lettura del tag: " + e.getMessage() + "\n";
        }
    }

    private String parseTextRecord(NdefRecord record) {
        if (record.getTnf() == NdefRecord.TNF_WELL_KNOWN &&
            Arrays.equals(record.getType(), NdefRecord.RTD_TEXT)) {

            try {
                byte[] payload = record.getPayload();
                String textEncoding = ((payload[0] & 128) == 0) ? "UTF-8" : "UTF-16";
                int languageCodeLength = payload[0] & 0063;
                return new String(payload, languageCodeLength + 1,
                                payload.length - languageCodeLength - 1, textEncoding);
            } catch (UnsupportedEncodingException e) {
                return null;
            }
        }
        return null;
    }

    private String parseUriRecord(NdefRecord record) {
        if (record.getTnf() == NdefRecord.TNF_WELL_KNOWN &&
            Arrays.equals(record.getType(), NdefRecord.RTD_URI)) {

            byte[] payload = record.getPayload();
            if (payload.length > 0) {
                String uriPrefix = getUriPrefix(payload[0]);
                String uri = new String(payload, 1, payload.length - 1);
                return uriPrefix + uri;
            }
        }
        return null;
    }

    private String getUriPrefix(byte prefixCode) {
        switch (prefixCode) {
            case 0x01: return "http://www.";
            case 0x02: return "https://www.";
            case 0x03: return "http://";
            case 0x04: return "https://";
            case 0x05: return "tel:";
            case 0x06: return "mailto:";
            default: return "";
        }
    }

    private String getTnfDescription(short tnf) {
        switch (tnf) {
            case NdefRecord.TNF_EMPTY: return "Empty";
            case NdefRecord.TNF_WELL_KNOWN: return "Well Known";
            case NdefRecord.TNF_MIME_MEDIA: return "MIME Media";
            case NdefRecord.TNF_ABSOLUTE_URI: return "Absolute URI";
            case NdefRecord.TNF_EXTERNAL_TYPE: return "External Type";
            case NdefRecord.TNF_UNKNOWN: return "Unknown";
            case NdefRecord.TNF_UNCHANGED: return "Unchanged";
            default: return "Invalid TNF";
        }
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X ", b));
        }
        return result.toString().trim();
    }





    // ========== MODALITÀ KIOSK ==========

    private void initializeKioskMode() {
        kioskHandler = new Handler(Looper.getMainLooper());

        // Attiva automaticamente la modalità kiosk all'avvio
        enableKioskMode();
    }

    private void enableKioskMode() {
        isKioskMode = true;

        // Nasconde la barra di navigazione e la status bar
        hideSystemUI();

        // Aggiunge listener per intercettare cambiamenti dell'UI di sistema
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(visibility -> {
            if (isKioskMode) {
                // Se l'UI di sistema diventa visibile, nascondila di nuovo
                if ((visibility & View.SYSTEM_UI_FLAG_FULLSCREEN) == 0) {
                    hideSystemUI();
                }
            }
        });

        // Avvia il monitoraggio per riportare l'app in primo piano
        startKioskMonitoring();

        Toast.makeText(this, "Modalità Kiosk attivata", Toast.LENGTH_SHORT).show();
    }

    private void disableKioskMode() {
        // PRIMA cosa: disattiva immediatamente la modalità kiosk
        isKioskMode = false;

        // Ferma TUTTO il monitoraggio immediatamente
        stopKioskMonitoring();

        // Ferma anche l'handler per i tap admin per sicurezza
        if (adminTapHandler != null) {
            adminTapHandler.removeCallbacksAndMessages(null);
            if (resetAdminTapsRunnable != null) {
                adminTapHandler.removeCallbacks(resetAdminTapsRunnable);
                resetAdminTapsRunnable = null;
            }
        }

        // Reset completo dello stato dell'app
        isAppInForegroundState = true;
        lastForegroundTime = System.currentTimeMillis();
        adminButtonTaps = 0;

        // Ripristina immediatamente l'UI normale
        showSystemUI();

        Toast.makeText(this, "Modalità Kiosk disattivata - L'app si riavvierà per ripristinare la navigazione normale", Toast.LENGTH_LONG).show();

        // Forza un reset completo dell'activity per assicurarsi che tutto funzioni
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (!isKioskMode) { // Doppio controllo prima di ricreare
                recreate();
            }
        }, 1000); // Delay più lungo per dare tempo al toast
    }

    private void hideSystemUI() {
        try {
            // Nasconde l'UI di sistema ma permette le gesture
            getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            );

            // Solo i flag essenziali per la modalità kiosk
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

            // Rimuoviamo i flag più aggressivi che potrebbero interferire con le gesture
            // getWindow().addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);
            // getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
            // getWindow().addFlags(WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);

            // Impedisce screenshot solo in modalità kiosk
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE,
                               WindowManager.LayoutParams.FLAG_SECURE);

        } catch (Exception e) {
            // Continua anche se alcune impostazioni falliscono
            e.printStackTrace();
        }
    }

    private void showSystemUI() {
        try {
            // Ripristina l'UI di sistema normale
            getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);

            // Rimuovi tutti i flag della modalità kiosk
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);

            // Rimuovi il listener per i cambiamenti dell'UI di sistema
            getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(null);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void startKioskMonitoring() {
        // Non avviare se la modalità kiosk non è attiva
        if (!isKioskMode) {
            return;
        }

        // Ferma qualsiasi monitoraggio precedente
        stopKioskMonitoring();

        kioskRunnable = new Runnable() {
            @Override
            public void run() {
                // DOPPIO controllo per essere sicuri che siamo ancora in modalità kiosk
                if (isKioskMode) {
                    // Controlla se l'app è ancora in primo piano
                    if (!isAppInForeground()) {
                        // Riporta l'app in primo piano con metodi multipli
                        bringAppToFront();

                        // Riapplica le impostazioni UI dopo un breve ritardo
                        if (kioskHandler != null) {
                            kioskHandler.postDelayed(() -> {
                                if (isKioskMode) { // Triplo controllo
                                    hideSystemUI();
                                }
                            }, 100);
                        }
                    } else {
                        // Anche se l'app è in primo piano, riapplica le impostazioni UI
                        // per contrastare eventuali modifiche del sistema
                        if (isKioskMode) { // Controllo aggiuntivo
                            hideSystemUI();
                        }
                    }

                    // Ripeti il controllo solo se siamo ancora in modalità kiosk
                    if (isKioskMode && kioskHandler != null) {
                        kioskHandler.postDelayed(this, 300);
                    }
                }
            }
        };

        if (kioskHandler != null) {
            kioskHandler.post(kioskRunnable);
        }
    }

    private void stopKioskMonitoring() {
        if (kioskHandler != null) {
            if (kioskRunnable != null) {
                kioskHandler.removeCallbacks(kioskRunnable);
                kioskRunnable = null; // Imposta a null per evitare riutilizzo
            }
            // Rimuovi tutti i callback pendenti
            kioskHandler.removeCallbacksAndMessages(null);
        }
    }

    private boolean isAppInForeground() {
        // Usa il nostro tracking interno dello stato
        if (!isAppInForegroundState) {
            return false;
        }

        // Controllo aggiuntivo: se è passato troppo tempo dall'ultimo onResume
        long timeSinceLastForeground = System.currentTimeMillis() - lastForegroundTime;
        if (timeSinceLastForeground > 2000) { // 2 secondi
            return false;
        }

        // Controllo tramite ActivityManager per versioni supportate
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager != null) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    // Per Android 5.0+ usiamo il nostro tracking interno
                    // Questo è più affidabile delle API deprecate
                    return isAppInForegroundState;
                } else {
                    // Per versioni precedenti
                    ComponentName topActivity = activityManager.getRunningTasks(1).get(0).topActivity;
                    return topActivity.getPackageName().equals(getPackageName());
                }
            } catch (Exception e) {
                // In caso di errore, assumiamo che l'app non sia in primo piano
                return false;
            }
        }
        return isAppInForegroundState;
    }

    private void bringAppToFront() {
        try {
            // Metodo 1: Riporta l'activity corrente in primo piano
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                           Intent.FLAG_ACTIVITY_REORDER_TO_FRONT |
                           Intent.FLAG_ACTIVITY_SINGLE_TOP |
                           Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);

            // Metodo 2: Usa ActivityManager per portare il task in primo piano
            ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager != null) {
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        // Per Android 5.0+, usa moveTaskToFront se disponibile
                        activityManager.moveTaskToFront(getTaskId(), ActivityManager.MOVE_TASK_WITH_HOME);
                    }
                } catch (Exception e) {
                    // Ignora errori di sicurezza
                }
            }

            // Metodo 3: Forza il focus sulla finestra
            getWindow().getDecorView().requestFocus();

        } catch (Exception e) {
            // Log dell'errore ma continua
            e.printStackTrace();
        }
    }

    // Override dei pulsanti di sistema
    @Override
    public void onBackPressed() {
        if (isKioskMode) {
            // In modalità kiosk, mostra il dialog per il codice admin
            showAdminCodeDialog();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (isKioskMode) {
            // Blocca solo i tasti essenziali in modalità kiosk
            switch (keyCode) {
                case KeyEvent.KEYCODE_HOME:
                case KeyEvent.KEYCODE_RECENT_APPS:
                case KeyEvent.KEYCODE_MENU:
                case KeyEvent.KEYCODE_SEARCH:
                case KeyEvent.KEYCODE_APP_SWITCH:
                    return true; // Blocca il tasto
                case KeyEvent.KEYCODE_BACK:
                    showAdminCodeDialog();
                    return true;
                // Non blocchiamo più POWER, VOLUME, ASSIST per evitare problemi
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (isKioskMode) {
            // Blocca solo i tasti essenziali in modalità kiosk
            switch (keyCode) {
                case KeyEvent.KEYCODE_HOME:
                case KeyEvent.KEYCODE_RECENT_APPS:
                case KeyEvent.KEYCODE_MENU:
                case KeyEvent.KEYCODE_SEARCH:
                case KeyEvent.KEYCODE_APP_SWITCH:
                case KeyEvent.KEYCODE_BACK:
                    return true; // Blocca il tasto
            }
        }
        return super.onKeyUp(keyCode, event);
    }

    @Override
    public boolean onKeyLongPress(int keyCode, KeyEvent event) {
        if (isKioskMode) {
            // Blocca solo i tasti essenziali in modalità kiosk
            switch (keyCode) {
                case KeyEvent.KEYCODE_HOME:
                case KeyEvent.KEYCODE_RECENT_APPS:
                case KeyEvent.KEYCODE_MENU:
                case KeyEvent.KEYCODE_SEARCH:
                case KeyEvent.KEYCODE_APP_SWITCH:
                case KeyEvent.KEYCODE_BACK:
                    return true; // Blocca il tasto
            }
        }
        return super.onKeyLongPress(keyCode, event);
    }

    private void showAdminCodeDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Codice Amministratore");
        builder.setMessage("Inserisci il codice per uscire dalla modalità kiosk\n" +
                          "Tentativi rimasti: " + (MAX_FAILED_ATTEMPTS - failedAttempts));
        builder.setCancelable(false);

        // Crea il layout per l'input
        LinearLayout layout = new LinearLayout(this);
        layout.setOrientation(LinearLayout.VERTICAL);
        layout.setPadding(50, 40, 50, 10);

        final EditText codeInput = new EditText(this);
        codeInput.setHint("Inserisci codice");
        codeInput.setInputType(android.text.InputType.TYPE_CLASS_NUMBER |
                              android.text.InputType.TYPE_NUMBER_VARIATION_PASSWORD);
        layout.addView(codeInput);

        builder.setView(layout);

        builder.setPositiveButton("Conferma", (dialog, which) -> {
            String enteredCode = codeInput.getText().toString().trim();
            validateAdminCode(enteredCode);
        });

        builder.setNegativeButton("Annulla", (dialog, which) -> {
            dialog.dismiss();
            // Rimane in modalità kiosk
        });

        AlertDialog dialog = builder.create();

        // Impedisce la chiusura del dialog premendo fuori
        dialog.setCanceledOnTouchOutside(false);

        // Mostra il dialog
        dialog.show();

        // Focus automatico sull'input
        codeInput.requestFocus();
    }

    private void validateAdminCode(String enteredCode) {
        if (ADMIN_CODE.equals(enteredCode)) {
            // Codice corretto
            failedAttempts = 0;
            disableKioskMode();
            Toast.makeText(this, "Accesso autorizzato. Modalità kiosk disattivata.", Toast.LENGTH_LONG).show();
        } else {
            // Codice errato
            failedAttempts++;

            if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
                // Troppi tentativi falliti
                Toast.makeText(this, "Troppi tentativi falliti. Riavvia l'app per riprovare.", Toast.LENGTH_LONG).show();
                failedAttempts = 0; // Reset per il prossimo ciclo
            } else {
                // Mostra di nuovo il dialog
                Toast.makeText(this, "Codice errato. Tentativi rimasti: " +
                              (MAX_FAILED_ATTEMPTS - failedAttempts), Toast.LENGTH_SHORT).show();

                // Ritardo prima di mostrare di nuovo il dialog
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    showAdminCodeDialog();
                }, 1000);
            }
        }
    }
}