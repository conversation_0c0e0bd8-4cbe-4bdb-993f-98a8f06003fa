package com.example.jetnfctester;

import android.content.Intent;
import android.os.Bundle;
import android.widget.TextView;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

/**
 * Activity principale dell'applicazione NFC Tester
 * Gestisce la lettura dei tag NFC
 */
public class MainActivity extends AppCompatAcFLAG_ACTIVITY_REORDER_TO_FRONTtivity implements
    NFCManager.NFCListener {

    private static final String TAG = "MainActivity";

    // UI Components
    private TextView statusTextView;
    private TextView nfcDataTextView;

    // Managers
    private NFCManager nfcManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        // Inizializza le view
        initializeViews();

        // Inizializza i manager
        initializeManagers();

        // Gestisci l'intent se l'app è stata aperta da un tag NFC
        nfcManager.handleIntent(getIntent());
    }

    /**
     * Inizializza le view dell'interfaccia utente
     */
    private void initializeViews() {
        statusTextView = findViewById(R.id.statusTextView);
        nfcDataTextView = findViewById(R.id.nfcDataTextView);

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
    }

    /**
     * Inizializza il manager NFC
     */
    private void initializeManagers() {
        // Inizializza NFCManager
        nfcManager = new NFCManager(this, this);
    }

    // ========== LIFECYCLE METHODS ==========

    @Override
    protected void onResume() {
        super.onResume();

        // Gestisci NFC
        nfcManager.checkStatus();
        nfcManager.enableForegroundDispatch();
    }

    @Override
    protected void onPause() {
        super.onPause();

        // Gestisci NFC
        nfcManager.disableForegroundDispatch();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        nfcManager.handleIntent(intent);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Nessuna pulizia necessaria
    }

    // ========== NFC LISTENER IMPLEMENTATION ==========

    @Override
    public void onNFCStatusChanged(String status) {
        runOnUiThread(() -> {
            if (statusTextView != null) {
                statusTextView.setText(status);
            }
        });
    }

    @Override
    public void onNFCDataReceived(String data) {
        runOnUiThread(() -> {
            if (nfcDataTextView != null) {
                nfcDataTextView.setText(data);
            }
        });
    }

    @Override
    public void onNFCError(String error) {
        runOnUiThread(() -> {
            if (nfcDataTextView != null) {
                nfcDataTextView.setText(error);
            }
        });
    }
}
