package com.example.jetnfctester;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import androidx.activity.EdgeToEdge;
import androidx.activity.OnBackPressedCallback;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

/**
 * Activity principale dell'applicazione NFC Tester
 * Coordina i vari manager per NFC, Kiosk e Admin
 */
public class MainActivity extends AppCompatActivity implements
    NFCManager.NFCListener,
    KioskManager.KioskListener,
    AdminManager.AdminListener {

    private static final String TAG = "MainActivity";

    // UI Components
    private TextView statusTextView;
    private TextView nfcDataTextView;

    // Managers
    private NFCManager nfcManager;
    private KioskManager kioskManager;
    private AdminManager adminManager;

    // Back press callback
    private OnBackPressedCallback backPressedCallback;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        // Inizializza le view
        initializeViews();

        // Inizializza i manager
        initializeManagers();

        // Gestisci l'intent se l'app è stata aperta da un tag NFC
        nfcManager.handleIntent(getIntent());
    }

    /**
     * Inizializza le view dell'interfaccia utente
     */
    private void initializeViews() {
        statusTextView = findViewById(R.id.statusTextView);
        nfcDataTextView = findViewById(R.id.nfcDataTextView);

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
    }

    /**
     * Inizializza tutti i manager
     */
    private void initializeManagers() {
        // Inizializza NFCManager
        nfcManager = new NFCManager(this, this);

        // Inizializza KioskManager
        kioskManager = new KioskManager(this, this);

        // Inizializza AdminManager
        adminManager = new AdminManager(this, this);

        // Configura il pulsante admin nascosto
        View adminButton = findViewById(R.id.adminButton);
        adminManager.setupAdminButton(adminButton);

        // Inizializza la modalità kiosk
        kioskManager.initialize();

        // Configura il callback per il pulsante back
        setupBackPressedCallback();
    }

    /**
     * Configura il callback per il pulsante back (nuovo sistema Android)
     */
    private void setupBackPressedCallback() {
        backPressedCallback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                Log.d(TAG, "Back pressed - KioskMode: " + (kioskManager != null ? kioskManager.isKioskModeEnabled() : "null"));

                if (kioskManager != null && kioskManager.isKioskModeEnabled()) {
                    // In modalità kiosk, mostra il dialog per il codice admin
                    if (adminManager != null) {
                        adminManager.showAdminCodeDialog();
                    }
                } else {
                    // Comportamento normale: chiudi l'activity
                    setEnabled(false); // Disabilita temporaneamente il callback
                    getOnBackPressedDispatcher().onBackPressed(); // Chiama il comportamento predefinito
                }
            }
        };

        // Registra il callback
        getOnBackPressedDispatcher().addCallback(this, backPressedCallback);
    }

    // ========== LIFECYCLE METHODS ==========

    @Override
    protected void onResume() {
        super.onResume();

        // Gestisci NFC
        nfcManager.checkStatus();
        nfcManager.enableForegroundDispatch();

        // Gestisci Kiosk
        kioskManager.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();

        // Gestisci NFC
        nfcManager.disableForegroundDispatch();

        // Gestisci Kiosk
        kioskManager.onPause();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        nfcManager.handleIntent(intent);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Pulisci le risorse
        if (kioskManager != null) {
            kioskManager.cleanup();
        }
        if (adminManager != null) {
            adminManager.cleanup();
        }

        // Rimuovi il callback del pulsante back
        if (backPressedCallback != null) {
            backPressedCallback.remove();
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (kioskManager != null) {
            kioskManager.onWindowFocusChanged(hasFocus);
        }
    }

    // ========== KEY EVENT HANDLING ==========

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        String keyName = KeyEvent.keyCodeToString(keyCode);
        Log.d(TAG, "KeyDown: " + keyName + " (KioskMode: " + (kioskManager != null ? kioskManager.isKioskModeEnabled() : "null") + ")");

        if (kioskManager != null && kioskManager.handleKeyDown(keyCode)) {
            return true; // Gestito dal KioskManager
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (kioskManager != null && kioskManager.handleKeyUp(keyCode)) {
            return true; // Gestito dal KioskManager
        }
        return super.onKeyUp(keyCode, event);
    }

    @Override
    public boolean onKeyLongPress(int keyCode, KeyEvent event) {
        if (kioskManager != null && kioskManager.handleKeyLongPress(keyCode)) {
            return true; // Gestito dal KioskManager
        }
        return super.onKeyLongPress(keyCode, event);
    }

    // ========== NFC LISTENER IMPLEMENTATION ==========

    @Override
    public void onNFCStatusChanged(String status) {
        runOnUiThread(() -> {
            if (statusTextView != null) {
                statusTextView.setText(status);
            }
        });
    }

    @Override
    public void onNFCDataReceived(String data) {
        runOnUiThread(() -> {
            if (nfcDataTextView != null) {
                nfcDataTextView.setText(data);
            }
        });
    }

    @Override
    public void onNFCError(String error) {
        runOnUiThread(() -> {
            if (nfcDataTextView != null) {
                nfcDataTextView.setText(error);
            }
        });
    }

    // ========== KIOSK LISTENER IMPLEMENTATION ==========

    @Override
    public void onKioskModeChanged(boolean enabled) {
        // Callback quando cambia lo stato della modalità kiosk
        // Può essere utilizzato per aggiornare l'UI se necessario
    }

    @Override
    public void onRequestAdminCode() {
        // Richiesta di mostrare il dialog del codice admin
        if (adminManager != null) {
            adminManager.showAdminCodeDialog();
        }
    }

    // ========== ADMIN LISTENER IMPLEMENTATION ==========

    @Override
    public void onKioskModeToggleRequested() {
        if (kioskManager != null) {
            if (kioskManager.isKioskModeEnabled()) {
                kioskManager.disableKioskMode();
            } else {
                kioskManager.enableKioskMode();
            }
        }
    }

    @Override
    public boolean isKioskModeEnabled() {
        return kioskManager != null && kioskManager.isKioskModeEnabled();
    }

    @Override
    public void onForceResetRequested() {
        if (kioskManager != null) {
            kioskManager.forceCompleteReset();
        }
    }

    @Override
    public void onDebugStateRequested() {
        Log.i(TAG, "=== DEBUG STATE REQUESTED ===");
        if (kioskManager != null) {
            kioskManager.logCurrentState();
        }
        // Mostra anche un toast per confermare
        android.widget.Toast.makeText(this, "Debug state logged - Check Android Studio Logcat",
                                     android.widget.Toast.LENGTH_LONG).show();
    }
}
