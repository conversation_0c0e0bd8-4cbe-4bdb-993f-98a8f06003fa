package com.example.jetnfctester;

import android.app.Activity;
import android.app.AlertDialog;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Toast;

/**
 * Gestisce l'accesso amministratore e il menu admin
 */
public class AdminManager {

    private Activity activity;
    private static final String ADMIN_CODE = "1234"; // Codice admin (da cambiare in produzione)
    private int failedAttempts = 0;
    private static final int MAX_FAILED_ATTEMPTS = 3;

    // Gestione pulsante admin nascosto
    private int adminButtonTaps = 0;
    private static final int ADMIN_TAPS_REQUIRED = 5;
    private Handler adminTapHandler = new Handler(Looper.getMainLooper());
    private Runnable resetAdminTapsRunnable;

    // Interface per comunicare con l'activity
    public interface AdminListener {
        void onKioskModeToggleRequested();
        boolean isKioskModeEnabled();
        void onForceResetRequested();
        void onDebugStateRequested();
    }

    private AdminListener listener;

    public AdminManager(Activity activity, AdminListener listener) {
        this.activity = activity;
        this.listener = listener;
    }

    /**
     * Configura il pulsante admin nascosto
     */
    public void setupAdminButton(View adminButton) {
        adminButton.setOnClickListener(v -> {
            adminButtonTaps++;

            // Reset del contatore dopo 3 secondi se non si raggiunge il numero richiesto
            if (resetAdminTapsRunnable != null) {
                adminTapHandler.removeCallbacks(resetAdminTapsRunnable);
            }

            resetAdminTapsRunnable = () -> {
                adminButtonTaps = 0;
            };
            adminTapHandler.postDelayed(resetAdminTapsRunnable, 3000);

            if (adminButtonTaps >= ADMIN_TAPS_REQUIRED) {
                adminButtonTaps = 0;
                adminTapHandler.removeCallbacks(resetAdminTapsRunnable);
                showAdminMenu();
            } else {
                // Feedback visivo discreto
                if (adminButtonTaps == 1) {
                    Toast.makeText(activity, "Admin mode: " + adminButtonTaps + "/" + ADMIN_TAPS_REQUIRED,
                                  Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    /**
     * Mostra il menu amministratore
     */
    private void showAdminMenu() {
        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle("Menu Amministratore");

        String[] options = {
            listener.isKioskModeEnabled() ? "Disattiva Modalità Kiosk" : "Attiva Modalità Kiosk",
            "Forza Reset Navigazione",
            "Debug State (Log)",
            "Cambia Codice Admin",
            "Info App"
        };

        builder.setItems(options, (dialog, which) -> {
            switch (which) {
                case 0:
                    if (listener.isKioskModeEnabled()) {
                        showAdminCodeDialog();
                    } else {
                        listener.onKioskModeToggleRequested();
                    }
                    break;
                case 1:
                    listener.onForceResetRequested();
                    break;
                case 2:
                    listener.onDebugStateRequested();
                    break;
                case 3:
                    showChangeAdminCodeDialog();
                    break;
                case 4:
                    showAppInfo();
                    break;
            }
        });

        builder.setNegativeButton("Annulla", null);
        builder.show();
    }

    /**
     * Mostra il dialog per l'inserimento del codice admin
     */
    public void showAdminCodeDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle("Codice Amministratore");
        builder.setMessage("Inserisci il codice per uscire dalla modalità kiosk\n" +
                          "Tentativi rimasti: " + (MAX_FAILED_ATTEMPTS - failedAttempts));
        builder.setCancelable(false);

        // Crea il layout per l'input
        LinearLayout layout = new LinearLayout(activity);
        layout.setOrientation(LinearLayout.VERTICAL);
        layout.setPadding(50, 40, 50, 10);

        final EditText codeInput = new EditText(activity);
        codeInput.setHint("Inserisci codice");
        codeInput.setInputType(android.text.InputType.TYPE_CLASS_NUMBER |
                              android.text.InputType.TYPE_NUMBER_VARIATION_PASSWORD);
        layout.addView(codeInput);

        builder.setView(layout);

        builder.setPositiveButton("Conferma", (dialog, which) -> {
            String enteredCode = codeInput.getText().toString().trim();
            validateAdminCode(enteredCode);
        });

        builder.setNegativeButton("Annulla", (dialog, which) -> {
            dialog.dismiss();
            // Rimane in modalità kiosk
        });

        AlertDialog dialog = builder.create();

        // Impedisce la chiusura del dialog premendo fuori
        dialog.setCanceledOnTouchOutside(false);

        // Mostra il dialog
        dialog.show();

        // Focus automatico sull'input
        codeInput.requestFocus();
    }

    /**
     * Valida il codice admin inserito
     */
    private void validateAdminCode(String enteredCode) {
        if (ADMIN_CODE.equals(enteredCode)) {
            // Codice corretto
            failedAttempts = 0;
            listener.onKioskModeToggleRequested();
            Toast.makeText(activity, "Accesso autorizzato. Modalità kiosk disattivata.", Toast.LENGTH_LONG).show();
        } else {
            // Codice errato
            failedAttempts++;

            if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
                // Troppi tentativi falliti
                Toast.makeText(activity, "Troppi tentativi falliti. Riavvia l'app per riprovare.", Toast.LENGTH_LONG).show();
                failedAttempts = 0; // Reset per il prossimo ciclo
            } else {
                // Mostra di nuovo il dialog
                Toast.makeText(activity, "Codice errato. Tentativi rimasti: " +
                              (MAX_FAILED_ATTEMPTS - failedAttempts), Toast.LENGTH_SHORT).show();

                // Ritardo prima di mostrare di nuovo il dialog
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    showAdminCodeDialog();
                }, 1000);
            }
        }
    }

    /**
     * Mostra il dialog per cambiare il codice admin
     */
    private void showChangeAdminCodeDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle("Cambia Codice Admin");
        builder.setMessage("Funzionalità non implementata in questa versione demo.\n" +
                          "Il codice attuale è: " + ADMIN_CODE);
        builder.setPositiveButton("OK", null);
        builder.show();
    }

    /**
     * Mostra le informazioni dell'app
     */
    private void showAppInfo() {
        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle("Info App");
        builder.setMessage("NFC Tester v1.0\n\n" +
                          "Modalità Kiosk: " + (listener.isKioskModeEnabled() ? "ATTIVA" : "DISATTIVA") + "\n" +
                          "Codice Admin: " + ADMIN_CODE + "\n\n" +
                          "Funzionalità:\n" +
                          "• Lettura tag NFC\n" +
                          "• Modalità Kiosk\n" +
                          "• Controllo admin\n" +
                          "• Reset navigazione\n\n" +
                          "Per accedere al menu admin:\n" +
                          "Tocca 5 volte l'angolo in alto a destra\n\n" +
                          "NOTA: Se i pulsanti di navigazione non funzionano\n" +
                          "dopo aver disattivato il kiosk, usa 'Forza Reset Navigazione'");
        builder.setPositiveButton("OK", null);
        builder.show();
    }

    /**
     * Pulisce le risorse
     */
    public void cleanup() {
        if (adminTapHandler != null) {
            adminTapHandler.removeCallbacksAndMessages(null);
            if (resetAdminTapsRunnable != null) {
                adminTapHandler.removeCallbacks(resetAdminTapsRunnable);
                resetAdminTapsRunnable = null;
            }
        }
        adminButtonTaps = 0;
        failedAttempts = 0;
    }

    /**
     * Ottiene il codice admin attuale
     */
    public String getAdminCode() {
        return ADMIN_CODE;
    }

    /**
     * Ottiene il numero di tentativi falliti
     */
    public int getFailedAttempts() {
        return failedAttempts;
    }

    /**
     * Reset dei tentativi falliti
     */
    public void resetFailedAttempts() {
        failedAttempts = 0;
    }
}
